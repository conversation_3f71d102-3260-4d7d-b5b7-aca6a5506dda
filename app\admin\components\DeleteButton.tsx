'use client'

import IconButton from '@/components/IconButton'
import { Trash2 } from 'lucide-react'
import { useState } from 'react'
import DeleteAlert from './DeleteAlert'
import { toast } from 'sonner'

type Props = {
  id: string
}

const DeleteButton = ({ id }: Props) => {
  const [isDeleteAlertOpen, setDeleteAlertOpen] = useState(false)

  const handleClickDelete = () => {
    setDeleteAlertOpen(true)
  }

  const handleRemoveRecommendation = () => {
    toast.success('Recommendation deleted: ' + id)
    // TODO: Add delete logic
  }

  return (
    <div
      onClick={e => e.stopPropagation()}
      onKeyDown={e => {
        if (e.key === 'Enter') {
          e.stopPropagation()
          handleClickDelete()
        }
      }}
      role="button"
      tabIndex={0}
    >
      <IconButton
        variant="card"
        Icon={Trash2}
        onClick={handleClickDelete}
        className="p-2 h-auto hover:text-red-600"
      />
      <DeleteAlert
        isOpen={isDeleteAlertOpen}
        setIsOpen={setDeleteAlertOpen}
        handleDeleteRecommendation={handleRemoveRecommendation}
      />
    </div>
  )
}

export default DeleteButton
