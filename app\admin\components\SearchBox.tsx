'use client'

import { MAPBOX_CONFIG } from '@/lib/mapConfig'
import { SearchBox as MapboxSearchBox } from '@mapbox/search-js-react'
import { useState } from 'react'

type Props = {
  updateLocation: (coordinates: { lng: number; lat: number; placeAddress?: string }) => void
}

const SearchBox = ({ updateLocation }: Props) => {
  const [searchTerm, setSearchTerm] = useState('')

  return (
    // @ts-expect-error "'MapboxSearchBox' cannot be used as a JSX component." Error, Yes it can, even in the example it is.
    <MapboxSearchBox
      accessToken={MAPBOX_CONFIG.accessToken || ''}
      options={{
        language: 'en',
        country: 'hu',
        proximity: { lng: MAPBOX_CONFIG.center[0], lat: MAPBOX_CONFIG.center[1] },
        limit: 5,
      }}
      value={searchTerm}
      onChange={val => setSearchTerm(val)}
      onRetrieve={result => {
        const { features } = result
        if (!features.length) return
        const {
          geometry: {
            coordinates: [lng, lat],
          },
          properties: { full_address: placeAddress },
        } = features[0]
        updateLocation({ lng, lat, placeAddress })
      }}
      placeholder="Search for an address in Szeged..."
    />
  )
}

export default SearchBox
