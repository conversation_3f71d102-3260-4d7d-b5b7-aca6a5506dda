import { Category, CategoryDetails } from '@/types/types'
import { MapPin, Sparkles, Star, UtensilsCrossed, Wine, Zap } from 'lucide-react'

export const categoryDetails: Record<Category, CategoryDetails> = {
  eat: {
    id: 'eat',
    name: 'Eat',
    title: 'Culinary Discoveries',
    description: 'Local restaurants & cafes',
    subtitle: 'Handpicked restaurants & cafes',
    gradient: 'eat-foreground-gradient',
    bgGradient: 'eat-bg-gradient',
    shadowColor: 'shadow-eat-accent/20',
    icon: UtensilsCrossed,
    accentIcon: Sparkles,
    iconColor: 'text-eat-icon-color',
    emoji: '🍽️',
  },
  drink: {
    id: 'drink',
    name: 'Drink',
    title: 'Liquid Adventures',
    description: 'Bars & breweries',
    subtitle: 'Craft cocktails & local brews',
    gradient: 'drink-foreground-gradient',
    bgGradient: 'drink-bg-gradient',
    shadowColor: 'shadow-drink-accent/20',
    icon: Wine,
    accentIcon: Zap,
    iconColor: 'text-drink-icon-color',
    emoji: '🍸',
  },
  do: {
    id: 'do',
    name: 'Do',
    title: 'Urban Experiences',
    description: 'Activities & attractions',
    subtitle: 'Unique activities & hidden gems',
    gradient: 'do-foreground-gradient',
    bgGradient: 'do-bg-gradient',
    shadowColor: 'shadow-do-accent/20',
    icon: MapPin,
    accentIcon: Star,
    iconColor: 'text-do-icon-color',
    emoji: '🎯',
  },
}
