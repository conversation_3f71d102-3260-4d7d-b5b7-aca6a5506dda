import { isMapboxConfigured, MAPBOX_CONFIG } from '@/lib/mapConfig'
import { cn } from '@/lib/utils'
import { Map } from 'mapbox-gl'
import { RefObject, useEffect, useRef, useState } from 'react'
import MapOverlay from './MapOverlay'
import MapStatus from './MapStatus'

type Props = {
  map: Map | null
  mapLoaded: boolean
  setMapLoaded: () => void
}

const Mapbox = ({ map, mapLoaded, setMapLoaded }: Props) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const [isError, setIsError] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  useEffect(() => {
    if (map || !mapContainer.current || !isMapboxConfigured()) {
      setIsError(true)
      return
    }

    const initializeMap = async () => {
      try {
        const mapboxgl = await import('mapbox-gl')
        mapboxgl.default.accessToken = MAPBOX_CONFIG.accessToken

        map = new mapboxgl.default.Map({
          container: mapContainer.current!,
          style: MAPBOX_CONFIG.style,
          center: MAPBOX_CONFIG.center,
          zoom: 15,
          attributionControl: false,
        })

        map.on('load', () => {
          setMapLoaded()
        })
        map.on('error', () => setIsError(true))
        map.addControl(new mapboxgl.default.NavigationControl(), 'top-right')
      } catch {
        setIsError(true)
      }
    }

    initializeMap()

    return () => {
      if (map) map.remove()
    }
  }, [map, setMapLoaded])

  useEffect(() => {
    if (map && mapLoaded) {
      console.log('resize', map)
      map.resize()
    }
  }, [isFullscreen, mapLoaded, map])

  return (
    <section
      className={cn(
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : 'relative h-64 rounded-lg',
        'p-0 bg-slate-100 overflow-hidden shadow-lg transition-all duration-300'
      )}
    >
      <div
        ref={mapContainer}
        className={cn('relative size-full overflow-hidden', (!mapLoaded || isError) && 'hidden')}
      >
        <MapOverlay
          isFullscreen={isFullscreen}
          toggleFullscreen={() => setIsFullscreen(prev => !prev)}
          isEditing
        />
      </div>
      <MapStatus isError={isError} mapLoaded={mapLoaded} />
    </section>
  )
}

export default Mapbox
