'use client'

import { useEffect, useState } from 'react'
import { Recommendation } from '@/types/types'
import { useRecommendationForm } from '@/stores/recommendationForm.store'
import { useShallow } from 'zustand/shallow'
import { getRecommendationById } from '@/data/mockData'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/UI/dialog'
import { ScrollArea } from '@/components/UI/scroll-area'
import { Plus, Save } from 'lucide-react'
import RecommendationForm from './RecommendationForm'

const RecommendationFormDialog = () => {
  const { recommendationId, clearRecommendationId } = useRecommendationForm(
    useShallow(state => ({
      recommendationId: state.recommendationId,
      clearRecommendationId: state.clearRecommendationId,
    }))
  )
  const [isLoading, setIsLoading] = useState(true)
  const [recommendation, setRecommendation] = useState<Recommendation | null>(null)

  useEffect(() => {
    if (!recommendationId) return
    try {
      setIsLoading(true)
      if (recommendationId === 'new') {
        setRecommendation(null)
        return
      }
      setRecommendation(getRecommendationById(recommendationId))
    } catch (error) {
      console.error('Error fetching recommendation: ', error)
    } finally {
      setIsLoading(false)
    }
  }, [recommendationId])

  const handleClose = () => {
    setRecommendation(null)
    clearRecommendationId()
  }

  return (
    <Dialog open={!!recommendationId} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className="w-full max-w-full sm:max-w-4xl border border-gray-200 shadow-xl">
        <DialogHeader className="border-b border-gray-200 pb-4">
          <DialogTitle className="flex text-base font-normal items-center gap-3">
            <div className="size-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white">
              {recommendationId === 'new' ? <Plus size={20} /> : <Save size={20} />}
            </div>
            {recommendationId === 'new' ? 'Add New Recommendation' : 'Edit Recommendation'}
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[80dvh] pr-4 -mr-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="size-16 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>
          ) : (
            <RecommendationForm recommendation={recommendation} onClose={handleClose} />
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default RecommendationFormDialog
