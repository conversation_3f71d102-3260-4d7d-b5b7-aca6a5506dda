import { Badge } from '@/components/UI/badge'
import { LucideIcon } from 'lucide-react'
import { ComponentProps } from 'react'
import IconWithText from '@/components/IconWithText'

const iconSizes = {
  sm: 8,
  default: 12,
  lg: 16,
}

type Props = ComponentProps<typeof Badge> & {
  Icon: LucideIcon
  text?: string
  iconClassName?: string
}

const IconBadge = ({
  Icon,
  text,
  iconClassName,
  variant = 'glass',
  size = 'default',
  ...props
}: Props) => (
  <Badge variant={variant} size={size} {...props}>
    <IconWithText Icon={Icon} text={text} iconClassName={iconClassName} size={iconSizes[size!]} />
  </Badge>
)

export default IconBadge
