import { Card, CardContent } from '@/components/UI/card'
import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

type Props = {
  title: string
  value: number
  icon: LucideIcon
  iconClassname: string
}

const StatCard = ({ title, value, icon: Icon, iconClassname }: Props) => (
  <Card>
    <CardContent>
      <div className="flex items-center gap-3">
        <div className={cn('size-10 rounded-lg flex items-center justify-center', iconClassname)}>
          <Icon size={20} />
        </div>
        <div>
          <p className="text-xl font-semibold">{value}</p>
          <p className="text-sm text-gray-600">{title}</p>
        </div>
      </div>
    </CardContent>
  </Card>
)

export default StatCard
