import Dot from '@/components/Dot'
import { Square } from 'lucide-react'

const RandomGeometricShapes = () => (
  <div className="absolute inset-0 overflow-hidden">
    <div className="opacity-30 *:absolute *:animate-pulse *:blur-3xl *:text-transparent *:bg-gradient-to-br ">
      <Dot size={400} className="top-3/4 left-3/5 pulsating-orb-1-gradient" />
      <Dot
        size={384}
        style={{ animationDelay: '1s' }}
        className="top-1/10 left-4/5 pulsating-orb-2-gradient"
      />
      <Dot
        size={320}
        style={{ animationDelay: '2s' }}
        className="top-3/5 left-1/10 pulsating-orb-3-gradient"
      />
      <Dot
        size={288}
        style={{ animationDelay: '3s' }}
        className="top-1/20 left-1/5 pulsating-orb-4-gradient"
      />
    </div>
    <div className="opacity-10 *:absolute">
      <Square
        fill="currentColor"
        size={16}
        className="top-20 left-10 rotate-45 animate-spin text-pulsating-orb-1-from animation-duration-8000"
      />
      <Dot
        style={{ animationDelay: '1s' }}
        size={12}
        className="top-40 right-20 animate-ping text-pulsating-orb-2-from"
      />
      <Square
        fill="currentColor"
        style={{ animationDelay: '2s' }}
        size={24}
        className="bottom-32 left-20 rotate-12 animate-pulse text-pulsating-orb-3-from"
      />
      <Dot
        style={{ animationDelay: '3s' }}
        size={8}
        className="bottom-20 right-10 animate-bounce text-pulsating-orb-4-from"
      />
    </div>
  </div>
)

export default RandomGeometricShapes
