import { Qr<PERSON>ode, MapPin, Sparkles } from 'lucide-react'

const LogoIcon = () => (
  <div className="relative">
    <div className="absolute inset-0 rounded-xl blur-sm bg-gradient-to-br from-logo-from to-logo-via opacity-30" />
    <div className="relative rounded-xl flex items-center justify-center bg-gradient-to-br from-logo-from to-logo-via">
      <QrCode size={64} color="white" />
      <Sparkles
        size={24}
        className="absolute -top-1 -right-1 opacity-60 text-yellow-400"
        style={{ animationDelay: '0.5s' }}
      />
    </div>
    <div className="absolute -bottom-1 -right-1 rounded-full flex items-center justify-center bg-gradient-to-br from-emerald-400 to-teal-500 shadow-lg p-2.25">
      <MapPin size={18} color="white" />
    </div>
  </div>
)

export default LogoIcon
