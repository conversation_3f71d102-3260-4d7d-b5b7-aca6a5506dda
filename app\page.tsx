import ThemeSwitch from '@/components/ThemeSwitch'
import Logo from '@/app/components/Logo'
import Taglines from '@/app/components/Taglines'
import StatusIndicator from '@/app/components/StatusIndicator'
import CategoryCardList from '@/app/components/CategoryCardList'
import HomeFooter from '@/app/components/HomeFooter'
import Link from 'next/link'

const HomePage = () => (
  <div className="flex-1 flex flex-col items-center justify-center gap-5 py-10 max-sm:px-4">
    <main className="w-full max-w-sm space-y-5">
      <ThemeSwitch className="fixed top-4 right-4 z-50" size="sm" />
      <div className="flex flex-col items-center gap-5 opacity-0 animate-fade-up animation-duration-1000 **:transition-colors **:duration-300">
        <Logo />
        <Taglines />
        <StatusIndicator />
      </div>
      <CategoryCardList />
    </main>
    <HomeFooter />
    <div
      className="text-center transition-all duration-1000 opacity-0 animate-fade-up animation-duration-1000"
      style={{ transitionDelay: '1s' }}
    >
      <Link
        href="/admin"
        className="text-xs text-secondary-text opacity-30 hover:opacity-70 transition-all duration-300 hover:scale-105 focus:outline-none focus:backdrop-opacity-70"
      >
        Admin
      </Link>
    </div>
  </div>
)

export default HomePage
