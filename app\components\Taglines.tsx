import { Sparkles } from 'lucide-react'

const Taglines = () => (
  <div className="flex flex-col items-center gap-3">
    <p className="text-secondary-text text-sm font-semibold text-center">
      Discover hidden gems instantly
    </p>
    <div className="inline-flex items-center gap-2.5 px-3 py-1.5 rounded-full bg-gradient-to-r from-logo-from/10 to-logo-via/10">
      <Sparkles className="text-tagline-star" size={12} />
      <span className="text-center text-secondary-text line-clamp-2">
        Curated by locals, loved by travelers
      </span>
      <Sparkles className="text-tagline-star" size={12} />
    </div>
  </div>
)

export default Taglines
