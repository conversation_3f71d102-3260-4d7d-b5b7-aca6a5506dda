import React, { MouseEvent } from 'react'
import { Edit } from 'lucide-react'
import { useRecommendationForm } from '@/stores/recommendationForm.store'
import IconButton from '@/components/IconButton'
import { toast } from 'sonner'

type Props = {
  id: string
}

const EditButton = ({ id }: Props) => {
  const { setRecommendationId } = useRecommendationForm()

  const handleClickEdit = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    toast.success('Editing recommendation: ' + id)
    setRecommendationId(id)
  }

  return (
    <IconButton
      variant="card"
      Icon={Edit}
      onClick={handleClickEdit}
      className="p-2 h-auto hover:text-blue-600"
    />
  )
}

export default EditButton
