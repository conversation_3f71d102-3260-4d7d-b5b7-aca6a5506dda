import { ArrowLef<PERSON>, Sparkles } from 'lucide-react'
import IconButton from '@/components/IconButton'
import ThemeSwitch from '@/components/ThemeSwitch'
import GlassMorphismOverlay from '@/components/GlassMorphismOverlay'
import RandomGeometricShapes from '@/components/RandomGeometricShapes'
import GlassCard from '@/components/GlassCard'

const NotFound = () => (
  <div className="min-h-screen bg-gradient-to-br primary-gradient flex items-center justify-center relative overflow-hidden transition-colors duration-700">
    <ThemeSwitch size="sm" className="absolute top-6 right-6 z-50" />
    <GlassMorphismOverlay />
    <RandomGeometricShapes />
    <div className="relative text-center z-10 space-y-6 **:duration-300">
      <GlassCard className="gap-4 items-center" containerClassName="p-10 border-none">
        <div className="grid place-content-center size-16 rounded-full bg-gradient-to-br from-rose-400 to-pink-500">
          <Sparkles className="size-8 text-white" />
        </div>
        <div className="space-y-2">
          <h2 className="text-primary-text text-xl font-semibold">Recommendation not found</h2>
          <p className="text-secondary-text text-sm">This place might have moved or been removed</p>
        </div>
      </GlassCard>
      <IconButton
        className="bg-gradient-to-r from-blue-500 to-purple-500 hoverActive:from-blue-600 hoverActive:to-purple-600 text-white border-0 shadow-lg hoverActive:shadow-xl gap-4 !transition-everything"
        Icon={ArrowLeft}
        text="Go Home"
        link="/"
      />
    </div>
  </div>
)

export default NotFound
