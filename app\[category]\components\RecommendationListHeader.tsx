'use client'

import { cn } from '@/lib/utils'
import { Filter, MapPin, Search, Sparkles } from 'lucide-react'
import IconBadge from '@/components/IconBadge'
import IconButton from '@/components/IconButton'
import { Category } from '@/types/types'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import { useState } from 'react'

type Props = {
  category: Category
  recommendationsLength: number
}

const RecommendationListHeader = ({ category, recommendationsLength }: Props) => {
  const [{ bgGradient, subtitle }] = useState(() => getCategoryDetailsById(category))

  return (
    <section className="relative overflow-hidden text-center **:duration-300">
      <div className={cn('absolute inset-0 bg-gradient-to-br transition-colors', bgGradient)} />
      <div className="absolute inset-0 backdrop-blur-sm bg-white/5" />
      <div className="relative flex flex-col gap-6 px-6 py-12 animation-duration-1000 opacity-0 animate-fade-up max-w-md mx-auto">
        <h2 className="text-lg font-bold transition-colors">{subtitle}</h2>
        <div className="flex items-center justify-center gap-4">
          <IconBadge
            Icon={MapPin}
            text={`${recommendationsLength} places`}
            className="hoverActive:scale-105 border-glass-border"
            size="lg"
          />
          <IconBadge
            Icon={Sparkles}
            text="Curated"
            className="hoverActive:scale-105 border-glass-border"
            iconClassName="text-yellow-400"
            size="lg"
          />
        </div>
        <div className="flex items-center justify-center gap-4">
          <IconButton
            size="sm"
            variant="frostedGlass"
            text="Search"
            Icon={Search}
            className="hoverActive:scale-105"
          />
          <IconButton
            size="sm"
            variant="frostedGlass"
            text="Filter"
            Icon={Filter}
            className="hoverActive:scale-105"
          />
        </div>
      </div>
    </section>
  )
}

export default RecommendationListHeader
