import type { Metadata } from 'next'
import { ReactNode } from 'react'
import '@/app/globals.css'
import RandomGeometricShapes from '@/components/RandomGeometricShapes'
import GlassMorphismOverlay from '@/components/GlassMorphismOverlay'
import { ThemeProvider } from '@/components/UI/theme-provider'
import { Toaster } from '@/components/UI/sonner'

export const metadata: Metadata = {
  title: 'QR Guide',
}

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode
}>) => (
  <html lang="en" suppressHydrationWarning>
    <body>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <div className="relative flex flex-col min-h-screen text-primary-text bg-gradient-to-br primary-gradient transition-colors duration-700">
          <RandomGeometricShapes />
          <GlassMorphismOverlay />
          <div className="flex flex-col flex-1 z-10">{children}</div>
        </div>
        <Toaster position="bottom-right" richColors />
      </ThemeProvider>
    </body>
  </html>
)

export default RootLayout
