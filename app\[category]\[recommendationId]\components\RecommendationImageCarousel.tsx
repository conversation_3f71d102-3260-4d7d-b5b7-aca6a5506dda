'use client'

import { type CarouselApi } from '@/components/UI/carousel'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/UI/carousel'
import { ReactNode, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import ImageWithFallback from '@/components/ImageWithFallback'
import { Recommendation } from '@/types/types'

type Props = {
  images: Recommendation['images']
  name?: Recommendation['name']
  overlay?: ReactNode
}

const RecommendationImageCarousel = ({ images, name = '', overlay }: Props) => {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!isLoading) return

    const checkImageLoaded = () => {
      const image = new Image()
      image.src = images[0]

      const handleComplete = () => {
        setIsLoading(false)
      }

      image.onload = handleComplete
      image.onerror = handleComplete
    }

    checkImageLoaded()
  }, [images, isLoading])

  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)

  useEffect(() => {
    if (!api) return
    setCurrent(api.selectedScrollSnap() + 1)
    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  return (
    <Carousel
      opts={{
        align: 'start',
        loop: true,
      }}
      setApi={setApi}
      className="relative aspect-4/3"
    >
      <div
        className={cn(
          'absolute size-full bg-gray-300',
          !isLoading && 'opacity-0 transition-opacity duration-1000'
        )}
      />
      <CarouselContent className="m-0">
        {images.map((image, index) => (
          <CarouselItem key={index} className="p-0">
            <div className={cn(isLoading ? 'opacity-0' : 'animate-fade-up', 'aspect-4/3')}>
              <ImageWithFallback src={image} alt={name} className="size-full object-cover" />
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      {overlay}
      <div className="absolute bottom-4 justify-center inset-x-0 text-muted-foreground flex gap-2 z-20">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              api?.scrollTo(index)
            }}
            className={cn(
              'size-3 rounded-full transition-[background-color,scale] duration-300',
              index === current - 1
                ? 'bg-white shadow-lg scale-125'
                : 'bg-white/50 hoverActive:bg-white/75'
            )}
          />
        ))}
      </div>
      <CarouselPrevious
        className="top-1/2 left-2 size-auto aspect-square duration-300 hoverActive:scale-110 backdrop-blur-xl"
        size="sm"
        variant="card"
      />
      <CarouselNext
        className="top-1/2 right-2 size-auto aspect-square duration-300 hoverActive:scale-110 backdrop-blur-xl"
        size="sm"
        variant="card"
      />
    </Carousel>
  )
}

export default RecommendationImageCarousel
