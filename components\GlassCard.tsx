import { Card } from '@/components/UI/card'
import { cn } from '@/lib/utils'
import { ReactNode } from 'react'

type Props = {
  children: ReactNode
  containerClassName?: string
  className?: string
  variant?: 'glass' | 'frostedGlass'
}

const GlassCard = ({ children, className, containerClassName, variant = 'glass' }: Props) => (
  <Card
    className={cn(
      'relative gap-0 p-5 text-primary-text bg-card-bg backdrop-blur-xl border-[1px] border-glass-border shadow-lg rounded-xl transition-everything duration-300',
      containerClassName
    )}
  >
    {variant === 'frostedGlass' && (
      <div className="absolute inset-0.5 rounded-xl bg-gradient-to-t from-transparent via-white/5 to-white/10" />
    )}
    <div className={cn('relative flex flex-col', className)}>{children}</div>
  </Card>
)

export default GlassCard
