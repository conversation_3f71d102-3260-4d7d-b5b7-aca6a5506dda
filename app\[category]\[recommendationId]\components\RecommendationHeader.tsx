'use client'

import { cn } from '@/lib/utils'
import { Recommendation } from '@/types/types'
import { Star } from 'lucide-react'
import IconBadge from '@/components/IconBadge'
import LikeButton from '@/components/LikeButton'
import ShareButton from '@/components/ShareButton'
import Header from '@/components/Header'
import HeaderTitle from '@/components/HeaderTitle'
import getHeaderScrollConfig from '@/lib/getHeaderScrollConfig'
import useScrolled from '@/hooks/useScrolled'

type Props = Pick<Recommendation, 'id' | 'name' | 'description' | 'rating' | 'googleMaps'>

const RecommendationHeader = ({ id, name, description, rating, googleMaps }: Props) => {
  const { scrolled } = useScrolled()
  const headerScrollConfig = getHeaderScrollConfig(scrolled)
  const { buttonScale, buttonPadding, buttonFrontSize, iconSize } = headerScrollConfig

  return (
    <Header>
      <HeaderTitle
        title={name}
        StatBadges={
          <IconBadge
            Icon={Star}
            text={rating?.toString()}
            variant="frostedGlass"
            className="border-none"
            iconClassName="text-amber-400 fill-amber-400"
            size="sm"
          />
        }
        className={scrolled ? 'opacity-100' : 'opacity-0'}
        {...headerScrollConfig}
      />
      <div className="flex items-center gap-2">
        <LikeButton
          iconClassName={iconSize}
          recommendationId={id}
          className={cn(buttonScale, buttonPadding, buttonFrontSize)}
        />
        <ShareButton
          iconClassName={iconSize}
          title={name}
          text={description}
          url={googleMaps}
          className={cn(buttonScale, buttonPadding, buttonFrontSize)}
        />
      </div>
    </Header>
  )
}

export default RecommendationHeader
