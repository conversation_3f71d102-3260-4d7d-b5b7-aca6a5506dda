import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

type Props = {
  icon: LucideIcon
  accentIcon?: LucideIcon
  gradient: string
  shadowColor: string
}

const CategoryCardIcon = ({
  icon: IconComponent,
  accentIcon: AccentIcon,
  gradient,
  shadowColor,
}: Props) => (
  <div className="relative">
    <div
      className={cn(
        'absolute -inset-1 bg-gradient-to-br transition-everything',
        gradient,
        'rounded-3xl blur opacity-70 group-hoverActive:opacity-100'
      )}
    />
    <div
      className={cn(
        'relative p-3 rounded-2xl bg-gradient-to-br transition-everything',
        gradient,
        shadowColor,
        'shadow-[0_6px_20px,0_3px_8px]'
      )}
    >
      <IconComponent size={20} color="white" />
      {AccentIcon && (
        <AccentIcon
          size={12}
          className="absolute -top-0.5 -right-0.5 text-white/80 opacity-0 group-hoverActive:opacity-100 transition-opacity animate-pulse !duration-300"
        />
      )}
    </div>
  </div>
)

export default CategoryCardIcon
