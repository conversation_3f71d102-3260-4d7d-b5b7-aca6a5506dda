import { cn } from '@/lib/utils'
import { CategoryDetails, ScrollConfig } from '@/types/types'
import { ReactNode } from 'react'

type PickedScrollProps = Pick<
  ScrollConfig,
  | 'buttonScale'
  | 'buttonPadding'
  | 'buttonFrontSize'
  | 'iconSize'
  | 'titleFontSize'
  | 'statsOpacity'
  | 'statsGap'
  | 'statsMaxHeight'
>

type Props = PickedScrollProps & {
  title: CategoryDetails['title']
  Icon?: CategoryDetails['icon']
  emoji?: CategoryDetails['emoji']
  iconColor?: CategoryDetails['iconColor']
  scrolled?: boolean
  StatBadges?: ReactNode
  className?: string
}

const HeaderTitle = ({
  className,
  Icon,
  statsGap,
  StatBadges,
  titleFontSize,
  title,
  emoji,
  iconSize,
  scrolled,
  iconColor,
  statsOpacity,
  statsMaxHeight,
}: Props) => (
  <div className={cn(statsGap, 'flex flex-col min-w-0', className)}>
    <div
      className={cn(
        titleFontSize,
        'flex items-center gap-2 text-center *:transition-colors font-semibold'
      )}
    >
      {Icon && <Icon className={cn(iconSize, 'shrink-0 ', scrolled && iconColor)} />}
      <h1 className="line-clamp-2 min-w-0">{title}</h1>
      {emoji && <span className="shrink-0">{emoji}</span>}
    </div>
    {StatBadges && (
      <div className={cn('flex justify-center gap-3', statsOpacity, statsMaxHeight)}>
        {StatBadges}
      </div>
    )}
  </div>
)

export default HeaderTitle
