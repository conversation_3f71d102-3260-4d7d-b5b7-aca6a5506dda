import { Star, StarHalf } from 'lucide-react'
import { getStarRating } from '@/lib/recommendationHelper'

type Props = {
  rating: number
}

const StarRating = ({ rating }: Props) => (
  <div className="flex items-center gap-1.5">
    {getStarRating(rating).map(({ filled }, index) => (
      <div key={index} className="relative">
        {filled === 'half' ? (
          <>
            <StarHalf size={16} fill="currentColor" className="text-amber-400 drop-shadow-sm" />
            <StarHalf size={16} className="absolute inset-0 -scale-x-100 text-accent-text/50" />
          </>
        ) : (
          <Star
            size={16}
            fill={filled === 'full' ? 'currentColor' : 'none'}
            className={filled === 'full' ? 'text-amber-400 drop-shadow-sm' : 'text-accent-text/50'}
          />
        )}
      </div>
    ))}
  </div>
)

export default StarRating
