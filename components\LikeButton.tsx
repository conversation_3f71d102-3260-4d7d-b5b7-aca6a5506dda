'use client'

import { Heart } from 'lucide-react'
import { MouseEvent, useState } from 'react'
import { cn } from '@/lib/utils'
import IconButton from '@/components/IconButton'
import { toast } from 'sonner'

type Props = {
  recommendationId: string
  className?: string
  iconClassName?: string
}

const LikeButton = ({ className, iconClassName }: Props) => {
  const [isLiked, setIsLiked] = useState(false)

  const handleClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    toast.success(isLiked ? 'Removed from favorites' : 'Added to favorites!')
    setIsLiked(!isLiked)
  }

  return (
    <IconButton
      Icon={Heart}
      variant="card"
      className={cn('p-2.5 group/heart', className)}
      iconClassName={cn(
        'group-hover/heart:text-red-500 group-hover/heart:scale-110 duration-300',
        isLiked ? 'text-red-500 scale-110 fill-current' : 'text-secondary-text',
        iconClassName
      )}
      onClick={handleClick}
    />
  )
}

export default LikeButton
