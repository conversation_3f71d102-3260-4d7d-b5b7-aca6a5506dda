'use client'

import React, { useEffect, useRef, useCallback, useState } from 'react'
import { Button } from '@/components/UI/button'
import { Input } from '@/components/UI/input'
import { Label } from '@/components/UI/label'
import { <PERSON>hai<PERSON>, RotateCcw } from 'lucide-react'
import { MAPBOX_CONFIG } from '@/lib/mapConfig'
import { toast } from 'sonner'
import { Map, Marker } from 'mapbox-gl'
import { createRoot } from 'react-dom/client'
import MapMarker from './MapMarker'
import { reverseGeocode, generateGoogleMapsLink } from '@/lib/recommendationHelper'
import { Coordinates } from '@/types/types'
import { cn } from '@/lib/utils'
import dynamic from 'next/dynamic'
import Mapbox from '@/components/Mapbox'

const SearchBox = dynamic(() => import('./SearchBox'), { ssr: false })

type Props = {
  address: string
  coordinates: Coordinates
  onAddressChange: (address: string) => void
  onCoordinatesChange: (coordinates: Coordinates) => void
  onGoToGoogleMapsChange: (googleMapsLink: string) => void
  error?: string
}

const AddressPicker = ({
  address,
  coordinates,
  onAddressChange,
  onCoordinatesChange,
  onGoToGoogleMapsChange,
  error,
}: Props) => {
  const initialCoordinates = useRef(coordinates)
  const map = useRef<Map>(null)
  const marker = useRef<Marker>(null)
  const [mapLoaded, setMapLoaded] = useState(false)

  const updateLocation = useCallback(
    async ({ lng, lat, placeAddress }: { lng: number; lat: number; placeAddress?: string }) => {
      const addressText = placeAddress || (await reverseGeocode(lng, lat))
      if (marker.current) marker.current.setLngLat([lng, lat])
      if (map.current) {
        map.current.flyTo({
          center: [lng, lat],
          zoom: 18,
          duration: 1000,
        })
      }

      const googleMapsLink = generateGoogleMapsLink(lat, lng, addressText)

      onCoordinatesChange({ lat, lng })
      onAddressChange(addressText)
      onGoToGoogleMapsChange(googleMapsLink)
    },
    [onAddressChange, onCoordinatesChange, onGoToGoogleMapsChange]
  )

  const addMarker = useCallback(() => {
    if (!map.current) return

    const markerElement = document.createElement('div')
    const markerRoot = createRoot(markerElement)
    markerRoot.render(<MapMarker />)

    marker.current = new Marker({
      element: markerElement,
      draggable: true,
    })
      .setLngLat([initialCoordinates.current.lng, initialCoordinates.current.lat])
      .addTo(map.current)
    marker.current.on('dragend', () => {
      const LngLat = marker.current!.getLngLat()
      updateLocation(LngLat)
    })
  }, [updateLocation])

  const resetToSzeged = () => {
    updateLocation({
      lng: MAPBOX_CONFIG.center[0],
      lat: MAPBOX_CONFIG.center[1],
      placeAddress: 'Szeged, Csongrád-Csanád, Hungary',
    })
  }

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported')
      return
    }

    navigator.geolocation.getCurrentPosition(
      async position => {
        const { latitude: lat, longitude: lng } = position.coords
        updateLocation({ lng, lat })
      },
      () => {
        toast.error('Unable to get current location')
      }
    )
  }

  useEffect(() => {
    if (!mapLoaded || !map.current) return

    const handleClick = (e: mapboxgl.MapMouseEvent) => {
      const { lng, lat } = e.lngLat
      updateLocation({ lng, lat })
    }

    addMarker()
    map.current.setCenter([initialCoordinates.current.lng, initialCoordinates.current.lat])
    map.current.on('click', handleClick)

    const handleUnMount = () => {
      if (marker.current) {
        marker.current.remove()
        marker.current = null
      }
      if (map.current) map.current.off('click', handleClick)
    }

    return () => handleUnMount()
  }, [addMarker, mapLoaded, updateLocation])

  return (
    <div className="flex flex-col gap-4 px-1">
      <div className="space-y-2">
        <Label className={error && 'text-red-500'}>Selected Address *</Label>
        <Input
          contentEditable={false}
          value={address}
          placeholder="Address will appear here"
          className={cn('bg-gray-100', error && 'border-red-500')}
          readOnly
        />
      </div>

      <SearchBox updateLocation={updateLocation} />

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label>Location Preview</Label>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={getCurrentLocation} disabled={!mapLoaded}>
              <Crosshair className="size-3 mr-1" />
              Current
            </Button>
            <Button size="sm" variant="outline" onClick={resetToSzeged} disabled={!mapLoaded}>
              <RotateCcw className="size-3 mr-1" />
              Reset
            </Button>
          </div>
        </div>

        <Mapbox
          map={map.current}
          mapLoaded={mapLoaded}
          setMapLoaded={() => setMapLoaded(true)}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="latitude">Latitude</Label>
          <Input
            className="bg-gray-100"
            contentEditable={false}
            name="latitude"
            value={coordinates.lat}
            readOnly
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="longitude">Longitude</Label>
          <Input
            className="bg-gray-100"
            contentEditable={false}
            name="longitude"
            value={coordinates.lng}
            readOnly
          />
        </div>
      </div>
    </div>
  )
}

export default AddressPicker
