import IconButton from '@/components/IconButton'
import { cn } from '@/lib/utils'
import { ComponentProps } from 'react'

type Props = ComponentProps<typeof IconButton>

const SocialButton = ({ className, ...props }: Props) => (
  <IconButton
    className={cn(
      'bg-gradient-to-r text-white border-0 shadow-lg hoverActive:shadow-xl hoverActive:scale-105 duration-300',
      className
    )}
    {...props}
  />
)

export default SocialButton
