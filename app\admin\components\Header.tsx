'use client'

import IconButton from '@/components/IconButton'
import { useRecommendationForm } from '@/stores/recommendationForm.store'
import { Settings, Upload, Download, Plus } from 'lucide-react'
import React from 'react'
import { toast } from 'sonner'

const Header = () => {
  const setRecommendationId = useRecommendationForm(state => state.setRecommendationId)
  const importRef = React.useRef<HTMLInputElement>(null)

  const handleExportData = () => {
    try {
      toast.success('Data exported successfully')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export data')
    }
  }

  const handleImportData = () => {
    toast.success('Imported recommendations')
  }

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 shadow-sm z-50">
      <div className="flex items-center justify-between gap-4 flex-col md:flex-row max-w-screen-xl mx-auto p-4">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-4">
            <div className="size-10 rounded-lg bg-slate-600 flex items-center justify-center">
              <Settings size={20} color="white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold">Admin Dashboard</h1>
              <p className="text-sm text-gray-600">QR Guide Management Console</p>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-center flex-wrap gap-3">
          <input
            ref={importRef}
            type="file"
            accept=".json"
            onChange={handleImportData}
            className="hidden"
          />
          <IconButton
            variant="outline"
            size="sm"
            Icon={Upload}
            text="Import"
            onClick={() => importRef.current?.click()}
            className="rounded-lg"
          />
          <IconButton
            variant="outline"
            size="sm"
            Icon={Download}
            text="Export"
            onClick={handleExportData}
            className="rounded-lg"
          />
          <IconButton
            variant="active"
            size="sm"
            Icon={Plus}
            text="Add New"
            onClick={() => setRecommendationId('new')}
            className="rounded-lg"
          />
        </div>
      </div>
    </header>
  )
}

export default Header
