const MapMarker = () => (
  <div className="size-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full border-3 border-white shadow-xl flex items-center justify-center text-white cursor-pointer map-marker-inner transform transition-transform hover:scale-110">
    <svg className="size-5" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
        clipRule="evenodd"
      />
    </svg>
  </div>
)

export default MapMarker
