'use client'

import FormSection from './FormSection'
import { But<PERSON> } from '@/components/UI/button'
import { Input } from '@/components/UI/input'
import { Textarea } from '@/components/UI/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/UI/select'
import { Plus, Save, MapPin, Clock, Tag, Wifi } from 'lucide-react'
import { PRICE_LEVEL_MAP, RECOMMENDATION_CATEGORIES } from '@/data/recommendations'
import { COMMON_TAGS, COMMON_AMENITIES } from '@/data/mockData'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/UI/form'
import FormLabel from '@/components/FormLabel'
import { z } from 'zod'
import { cn } from '@/lib/utils'
import CheckboxCard from '@/components/CheckboxCard'
import IconButton from '@/components/IconButton'
import { useCallback, useState } from 'react'
import { SubmitHand<PERSON>, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { PriceLevel, Recommendation } from '@/types/types'
import AddressPicker from './AddressPicker'

const recommendationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Short description is required'),
  rating: z.number().min(0).max(5),
  priceLevel: z.enum(Object.keys(PRICE_LEVEL_MAP), {
    message: 'Price level is required',
  }),
  category: z.enum(RECOMMENDATION_CATEGORIES, {
    message: 'Category is required',
  }),
  address: z.string().min(1, 'Address is required'),
  hours: z.string().min(1, 'Opening hours are required'),
  image: z.string().min(1, 'Image is required'),
  tags: z.array(z.string()),
  phone: z.string().optional(),
  website: z.string().optional(),
  detailedDescription: z.string().min(50, 'Detailed description is required (min 50 characters)'),
  amenities: z.array(z.string()),
  images: z.array(z.string()).optional(),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number(),
  }),
  email: z.email().or(z.literal('')),
  social: z.object({
    instagram: z.string().optional(),
    facebook: z.string().optional(),
  }),
  googleMaps: z.string(),
  menuHighlights: z.array(z.string()).optional(),
})

type RecommendationFormValues = z.infer<typeof recommendationSchema>

const defaultValues = {
  name: '',
  category: '',
  description: '',
  address: '',
  priceLevel: '',
  rating: 4.0,
  tags: [],
  amenities: [],
  hours: '',
  social: {
    instagram: '',
    facebook: '',
  },
  coordinates: { lng: 20.14824, lat: 46.253 },
  images: [],
  menuHighlights: [],
  detailedDescription: '',
  email: '',
  googleMaps: '',
  image: '',
  phone: '',
  website: '',
}

type Props = {
  recommendation: Recommendation | null
  onClose: () => void
}

const getInitialValues = (recommendation: Recommendation | null) => {
  if (!recommendation) return defaultValues as RecommendationFormValues
  return {
    ...recommendation,
    priceLevel: String(recommendation.priceLevel),
  } as RecommendationFormValues
}

const RecommendationFormDialog = ({ recommendation, onClose }: Props) => {
  const [newTag, setNewTag] = useState('')
  const [newAmenity, setNewAmenity] = useState('')
  const [tags, setTags] = useState(
    new Set<string>([...COMMON_TAGS, ...(recommendation?.tags || [])])
  )
  const [amenities, setAmenities] = useState(
    new Set<string>([...COMMON_AMENITIES, ...(recommendation?.amenities || [])])
  )
  const form = useForm<RecommendationFormValues>({
    resolver: zodResolver(recommendationSchema),
    defaultValues: getInitialValues(recommendation),
  })
  const {
    watch,
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    reset,
  } = form

  const coordinates = watch('coordinates')
  const address = watch('address')

  const handleAddressChange = useCallback(
    (newAddress: string) => {
      setValue('address', newAddress)
    },
    [setValue]
  )

  const handleCoordinatesChange = useCallback(
    (newCoordinates: { lng: number; lat: number }) => {
      setValue('coordinates', newCoordinates)
    },
    [setValue]
  )

  const handleGoogleMapsChange = useCallback(
    (googleMapsLink: string) => {
      setValue('googleMaps', googleMapsLink)
    },
    [setValue]
  )

  const onSubmit: SubmitHandler<RecommendationFormValues> = useCallback(
    data => {
      // TODO upload new tags & amenities to database, then update the recommendation

      const newRecommendation: Recommendation = {
        ...data,
        id: recommendation?.id || `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        reviews: [],
        recommendedTravel: 'walking',
        travelTime: 0,
        priceLevel: data.priceLevel as unknown as PriceLevel,
        openingHours: {},
        images: data.images || [],
        menuHighlights: data.menuHighlights || [],
      }
      console.log('New recommendation: ', newRecommendation)
      onClose()
    },
    [onClose, recommendation?.id]
  )

  const handleClose = () => {
    reset()
    onClose()
  }

  const handleClickAddTag = () => {
    if (!newTag.trim()) return
    setTags(prev => new Set(prev.add(newTag.trim())))
    setNewTag('')
  }

  const handleClickAddAmenity = () => {
    if (!newAmenity.trim()) return
    setAmenities(prev => new Set(prev.add(newAmenity.trim())))
    setNewAmenity('')
  }

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col *:pb-8 gap-8 divide-y-[1px] divide-muted-foreground/40"
      >
        <FormSection title="Basic Information" icon={MapPin}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel text="Name" value="name" schema={recommendationSchema} />
                  <FormControl>
                    <Input {...field} placeholder="Enter place name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="category"
              render={({ field: { value, onChange } }) => (
                <FormItem>
                  <FormLabel text="Category" value="category" schema={recommendationSchema} />
                  <FormControl>
                    <Select onValueChange={onChange} defaultValue={value}>
                      <SelectTrigger
                        id="category"
                        className={cn('w-full', errors.category && 'border-red-500')}
                      >
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {RECOMMENDATION_CATEGORIES.map(cat => (
                          <SelectItem key={cat} value={cat}>
                            {cat}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel text="Description" value="description" schema={recommendationSchema} />
                <FormControl>
                  <Textarea {...field} placeholder="Short description" className="min-h-24" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="detailedDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel
                  text="Detailed Description"
                  value="detailedDescription"
                  schema={recommendationSchema}
                />
                <FormControl>
                  <Textarea {...field} placeholder="Detailed description" className="min-h-24" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* TODO: Image upload */}
          <FormField
            control={control}
            name="image"
            render={({ field }) => (
              <FormItem>
                <FormLabel text="Image" value="image" schema={recommendationSchema} />
                <FormControl>
                  <Input {...field} placeholder="Enter image URL" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* TODO: Image upload */}
          <FormField
            control={control}
            name="images"
            render={({ field }) => (
              <FormItem>
                <FormLabel text="Images" value="images" schema={recommendationSchema} />
                <FormControl>
                  <Input {...field} placeholder="Enter image URLs (comma separated)" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </FormSection>
        <FormSection title="Location" icon={MapPin}>
          <AddressPicker
            address={address}
            coordinates={coordinates}
            onAddressChange={handleAddressChange}
            onCoordinatesChange={handleCoordinatesChange}
            onGoToGoogleMapsChange={handleGoogleMapsChange}
            error={errors.address?.message}
          />
        </FormSection>
        <FormSection title="Details" icon={Clock}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="priceLevel"
              render={({ field: { value, onChange } }) => (
                <FormItem>
                  <FormLabel text="Price Level" value="priceLevel" schema={recommendationSchema} />
                  <FormControl>
                    <Select onValueChange={onChange} value={String(value)}>
                      <SelectTrigger
                        id="priceLevel"
                        className={cn('w-full', errors.priceLevel && 'border-red-500')}
                      >
                        <SelectValue placeholder="Select price level" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(PRICE_LEVEL_MAP).map(([key, val]) => (
                          <SelectItem key={key} value={key}>
                            {val}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="rating"
              render={({ field }) => (
                <FormItem>
                  <FormLabel text="Rating" value="rating" schema={recommendationSchema} />
                  <FormControl>
                    <Input {...field} type="number" min="0" max="5" step="0.1" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* TODO: Make an actual time picker */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="hours"
              render={({ field }) => (
                <FormItem>
                  <FormLabel text="Hours" value="hours" schema={recommendationSchema} />
                  <FormControl>
                    <Input type="tel" {...field} placeholder="e.g., Mon-Fri 9-18, Sat 10-16" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel text="Phone" value="phone" schema={recommendationSchema} />
                  <FormControl>
                    <Input {...field} placeholder="+36 XX XXX XXXX" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel text="Website" value="website" schema={recommendationSchema} />
                  <FormControl>
                    <Input {...field} placeholder="https://example.com" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel text="Email" value="email" schema={recommendationSchema} />
                  <FormControl>
                    <Input {...field} placeholder="<EMAIL>" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="social.instagram"
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    text="Instagram"
                    value="social.instagram"
                    schema={recommendationSchema}
                  />
                  <FormControl>
                    <Input {...field} placeholder="@username" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="social.facebook"
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    text="Facebook"
                    value="social.facebook"
                    schema={recommendationSchema}
                  />
                  <FormControl>
                    <Input {...field} placeholder="https://facebook.com/username" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </FormSection>
        <FormSection title="Tags" icon={Tag}>
          <FormField
            control={control}
            name="tags"
            render={() => (
              <FormItem className="flex-row flex-wrap">
                {Array.from(tags).map(tag => (
                  <FormField
                    key={tag}
                    control={control}
                    name="tags"
                    render={({ field: { value, onChange } }) => (
                      <FormItem>
                        <FormControl>
                          <CheckboxCard
                            name="tags"
                            label={tag}
                            size="sm"
                            checked={value.includes(tag)}
                            onCheckedChange={checked =>
                              onChange(checked ? [...value, tag] : value.filter(l => l !== tag))
                            }
                            gradient={'from-indigo-500 to-purple-600'}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                ))}
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex gap-2 items-center">
            <Input
              value={newTag}
              onChange={e => setNewTag(e.target.value)}
              placeholder="Add custom tag"
            />
            <Button
              type="button"
              onClick={handleClickAddTag}
              size="sm"
              variant="active"
              className="rounded-lg"
            >
              <Plus className="size-4" />
            </Button>
          </div>
        </FormSection>
        <FormSection title="Amenities" icon={Wifi}>
          <FormField
            control={control}
            name="amenities"
            render={() => (
              <FormItem className="flex-row flex-wrap">
                {Array.from(amenities).map(amenity => (
                  <FormField
                    key={amenity}
                    control={control}
                    name="amenities"
                    render={({ field: { value, onChange } }) => (
                      <FormItem>
                        <FormControl>
                          <CheckboxCard
                            name="amenities"
                            label={amenity}
                            size="sm"
                            checked={value.includes(amenity)}
                            onCheckedChange={checked =>
                              onChange(
                                checked ? [...value, amenity] : value.filter(l => l !== amenity)
                              )
                            }
                            gradient="from-emerald-500 to-teal-600"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                ))}
              </FormItem>
            )}
          />
          <div className="flex gap-2 items-center">
            <Input
              value={newAmenity}
              onChange={e => setNewAmenity(e.target.value)}
              placeholder="Add custom amenity"
            />
            <Button
              type="button"
              onClick={handleClickAddAmenity}
              size="sm"
              variant="active"
              className="rounded-lg"
            >
              <Plus className="size-4" />
            </Button>
          </div>
        </FormSection>
        <div className="flex flex-col sm:flex-row gap-4 !py-0">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="flex-1 !text-gray-700 !bg-white !border-gray-300 hover:!bg-gray-50 hover:!text-gray-900 rounded-md"
          >
            Cancel
          </Button>
          <IconButton
            Icon={Save}
            text={recommendation ? 'Update Recommendation' : 'Create Recommendation'}
            type="submit"
            className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 !text-white border-transparent hover:opacity-90 rounded-md"
          />
        </div>
      </form>
    </Form>
  )
}

export default RecommendationFormDialog
