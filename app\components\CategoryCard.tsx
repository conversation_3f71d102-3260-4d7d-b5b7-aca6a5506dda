'use client'

import { CategoryDetails } from '@/types/types'
import { useRouter } from 'next/navigation'
import { ChevronRight } from 'lucide-react'
import { Card, CardAction, CardDescription, CardHeader, CardTitle } from '@/components/UI/card'
import CategoryCardIcon from '@/app/components/CategoryCardIcon'
import CategoryCardBackground from '@/app/components/CategoryCardBackground'

type Props = {
  categoryDetail: CategoryDetails
  index: number
}

const CategoryCard = ({
  categoryDetail: {
    id,
    name,
    description,
    gradient,
    shadowColor,
    icon: IconComponent,
    accentIcon: AccentIcon,
    emoji,
  },
  index,
}: Props) => {
  const router = useRouter()
  const handleNavigation = () => router.push(`/${id}`)

  return (
    <Card
      className="group relative flex flex-row gap-3 overflow-hidden border-0 backdrop-blur-xl transition-all duration-700 hoverActive:scale-103 cursor-pointer p-4 w-full opacity-0 animate-fade-up !shadow-card-shadow bg-card-bg hoverActive:bg-card-hover **:duration-300"
      onClick={handleNavigation}
      style={{
        animationDelay: `${index * 150}ms`,
      }}
    >
      <CategoryCardBackground gradient={gradient} />
      <CategoryCardIcon
        icon={IconComponent}
        accentIcon={AccentIcon}
        gradient={gradient}
        shadowColor={shadowColor}
      />
      <CardHeader className="flex-1 px-0 gap-0 z-10">
        <CardTitle className="min-w-0 inline-flex items-center text-lg gap-1.5">
          <h3 className="truncate text-primary-text">{name}</h3>
          <span className="text-sm opacity-80">{emoji}</span>
        </CardTitle>
        <CardDescription className="text-xs font-semibold text-accent-text/90">
          {description}
        </CardDescription>
        <CardAction className="my-auto">
          <ChevronRight
            size={20}
            className="group-hoverActive:translate-x-1 shrink-0 text-accent-text group-hoverActive:text-primary-text transition-all !duration-300"
          />
        </CardAction>
      </CardHeader>
    </Card>
  )
}

export default CategoryCard
