import { Button } from '@/components/UI/button'
import { cn } from '@/lib/utils'
import { Category, CategoryDetails } from '@/types/types'
import { Star, MapPin, Clock } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

type MapPopupProps = {
  category: Category
  gradient: CategoryDetails['gradient']
  id: string
  name: string
  image: string
  rating: number
  recommendationCategory: string
  address: string
  hours: string
}

const RecommendationMapPopup = ({
  category,
  gradient,
  id,
  name,
  image,
  rating,
  recommendationCategory,
  address,
  hours,
}: MapPopupProps) => (
  <div className="flex flex-col gap-3">
    <div className="flex items-start gap-3">
      <div className="flex-shrink-0">
        <Image
          src={image}
          alt={name}
          width={64}
          height={64}
          className="rounded-lg size-16 object-cover"
        />
      </div>
      <div className="flex flex-col gap-0.5 flex-1 overflow-hidden">
        <h4 className="font-semibold text-slate-900 truncate">{name}</h4>
        <div className="flex items-center gap-1">
          <Star size={16} fill="currentColor" className="text-amber-400 drop-shadow-sm" />
          <span className="text-sm text-slate-600">{rating}</span>
        </div>
        <p className="text-slate-600/70">{recommendationCategory}</p>
      </div>
    </div>

    <div className="space-y-2 text-sm text-slate-600">
      <div className="flex items-start gap-2">
        <MapPin size={16} className="flex-shrink-0" />
        <span>{address}</span>
      </div>
      <div className="flex items-center gap-2">
        <Clock size={16} className="flex-shrink-0" />
        <span>{hours}</span>
      </div>
    </div>
    <Button
      className={cn(
        'w-full rounded-md bg-gradient-to-r text-white hover:shadow-lg duration-200',
        gradient
      )}
      asChild
    >
      <Link href={`/${category}/${id}`}>View Details</Link>
    </Button>
  </div>
)

export default RecommendationMapPopup
