'use client'

import { But<PERSON> } from '@/components/UI/button'
import { Input } from '@/components/UI/input'
import { Card, CardContent } from '@/components/UI/card'
import { Badge } from '@/components/UI/badge'
import { Grid3X3, List, Search } from 'lucide-react'
import { RECOMMENDATION_CATEGORIES } from '@/data/recommendations'
import IconButton from '@/components/IconButton'

type Props = {
  stats: {
    total: number
    byCategory: Record<string, number>
  }
  viewMode: 'grid' | 'table'
  setViewMode: (viewMode: 'grid' | 'table') => void
  searchQuery: string
  setSearchQuery: (searchQuery: string) => void
  selectedCategory: string
  setSelectedCategory: (selectedCategory: string) => void
}

const FilterCard = ({
  stats: { byCategory },
  viewMode,
  setViewMode,
  searchQuery,
  setSearchQuery,
  selectedCategory,
  setSelectedCategory,
}: Props) => (
  <Card>
    <CardContent>
      <div className="flex flex-col gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-gray-600" />
          <Input
            placeholder="Search recommendations..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex items-center gap-4 max-sm:flex-col-reverse">
          <div className="flex flex-1 gap-2 flex-wrap">
            {['all', ...RECOMMENDATION_CATEGORIES].map(category => (
              <Button
                key={category}
                size="sm"
                variant={selectedCategory === category ? 'active' : 'outline'}
                onClick={() => setSelectedCategory(category)}
                className="capitalize rounded-lg gap-3"
              >
                {category}
                {category !== 'all' && (
                  <Badge className="size-6 text-slate-900 bg-gray-100 rounded-md">
                    {byCategory[category] || 0}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
          <div className="flex self-start gap-1">
            <IconButton
              Icon={Grid3X3}
              size="sm"
              variant={viewMode === 'grid' ? 'active' : 'outline'}
              className="p-2 rounded-lg"
              onClick={() => setViewMode(viewMode === 'grid' ? 'table' : 'grid')}
            />
            <IconButton
              Icon={List}
              size="sm"
              variant={viewMode === 'table' ? 'active' : 'outline'}
              className="p-2 rounded-lg"
              onClick={() => setViewMode(viewMode === 'table' ? 'grid' : 'table')}
            />
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
)

export default FilterCard
