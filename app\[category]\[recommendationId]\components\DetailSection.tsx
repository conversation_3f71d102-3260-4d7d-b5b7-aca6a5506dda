import { LucideIcon } from 'lucide-react'
import { ElementType, ReactNode } from 'react'
import { cn } from '@/lib/utils'

type Props = {
  title: string
  children: ReactNode
  as?: ElementType
  Icon?: LucideIcon
  className?: string
  iconColor?: string
  animationDelay?: number
}

const DetailSection = ({
  Icon,
  as: Component = 'section',
  title,
  children,
  className,
  iconColor,
  animationDelay,
}: Props) => (
  <Component
    className={cn('flex flex-col gap-4 px-6 animate-fade-up', className)}
    style={{ animationDelay: `${animationDelay}ms` }}
  >
    <h3 className="inline-flex items-center gap-2 transition-colors duration-300 *:duration-300 text-xl font-semibold">
      {Icon && <Icon size={20} className={iconColor} />}
      {title}
    </h3>
    {children}
  </Component>
)

export default DetailSection
