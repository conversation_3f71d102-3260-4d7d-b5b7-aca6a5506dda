const getHeaderScrollConfig = (isScrolled: boolean) => ({
  headerPadding: isScrolled ? 'py-1.5 px-6' : 'py-6 px-4',
  headerOpacity: isScrolled ? 'bg-white/25' : 'bg-white/15',
  headerBlur: isScrolled ? 'backdrop-blur-[20px]' : 'backdrop-blur-[15px]',
  headerShadow: isScrolled
    ? 'shadow-[0_20px_40px_-12px_rgba(0,0,0,0.2),0_0_60px_color-mix(in_oklab,var(--theme-switch-secondary)12.5%,transparent)]'
    : 'shadow-[0_10px_25px_-5px_rgba(0,0,0,0.2)]',
  headerBorderColor: isScrolled
    ? 'border-[rgba(255,255,255,0.45)]'
    : 'border-[rgba(255,255,255,0.2)]',
  buttonScale: isScrolled ? 'scale-95' : 'scale-100',
  buttonPadding: isScrolled ? 'px-4 py-3' : 'px-4.5 py-4',
  buttonFrontSize: isScrolled ? 'text-sm' : 'text-base',
  iconSize: isScrolled ? 'size-4.5' : 'size-5',
  titleFontSize: isScrolled ? 'text-lg' : 'text-xl',
  statsGap: isScrolled ? 'gap-1' : 'gap-0',
  statsOpacity: isScrolled ? 'opacity-100' : 'opacity-0',
  statsMaxHeight: isScrolled ? 'max-h-8' : 'max-h-0',
})

export default getHeaderScrollConfig
