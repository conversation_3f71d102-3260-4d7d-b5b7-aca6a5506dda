import { Category } from '@/types/types'
import { notFound } from 'next/navigation'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import RecommendationHeader from '@/app/[category]/[recommendationId]/components/RecommendationHeader'
import GlassSeperator from '@/components/GlassSeperator'
import Footer from '@/app/[category]/[recommendationId]/components/Footer'
import ContactsHoursSection from '@/app/[category]/[recommendationId]/components/ContactsHoursSection'
import Highlights from '@/app/[category]/[recommendationId]/components/HighlightsSection'
import MainInformation from '@/app/[category]/[recommendationId]/components/MainInformationSection'
import Reviews from '@/app/[category]/[recommendationId]/components/ReviewsSection'
import { getRecommendationByCategoryAndId } from '@/data/mockData'

type Props = {
  params: Promise<{ category: string; recommendationId: string }>
}

const DetailsPage = async ({ params }: Props) => {
  const { category, recommendationId } = await params

  const recommendation = getRecommendationByCategoryAndId(category as Category, recommendationId)

  if (!recommendation) {
    notFound()
  }

  const { gradient, iconColor } = getCategoryDetailsById(category as Category)

  return (
    <>
      <RecommendationHeader {...recommendation} />
      <main className="max-w-md mx-auto space-y-8">
        <MainInformation categoryId={category as Category} {...recommendation} />
        <GlassSeperator />
        <Highlights gradient={gradient} iconColor={iconColor} {...recommendation} />
        <GlassSeperator />
        <ContactsHoursSection gradient={gradient} iconColor={iconColor} {...recommendation} />
        <GlassSeperator />
        <Reviews gradient={gradient} iconColor={iconColor} {...recommendation} />
        <GlassSeperator className="mb-8" />
      </main>
      <Footer iconColor={iconColor} recommendation={recommendation} />
    </>
  )
}

export default DetailsPage
