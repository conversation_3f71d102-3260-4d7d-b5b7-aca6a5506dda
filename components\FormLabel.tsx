import { ComponentProps } from 'react'
import { ZodOptional, ZodObject, ZodUnion } from 'zod'
import { FormLabel as ShadFormLabel } from '@/components/UI/form'

type Props = {
  text: string
  value: string
  schema: ZodObject
} & ComponentProps<typeof ShadFormLabel>

const FormLabel = ({ text, value, schema, ...props }: Props) => {
  const requiredFields = Object.entries(schema.shape).filter(
    ([, val]) => val && !(val instanceof ZodOptional) && !(val instanceof ZodUnion)
  )

  const isFieldRequired = (name: string) => requiredFields.some(([key]) => key === name)
  const isRequired = isFieldRequired(value)

  return (
    <ShadFormLabel htmlFor={value} {...props}>
      {text} {isRequired && '*'}
    </ShadFormLabel>
  )
}

export default FormLabel
