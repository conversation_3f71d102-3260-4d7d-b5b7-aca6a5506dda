import RecommendationCard from '@/components/RecommendationCard'
import { getCategoryDetailsByRecommendationId } from '@/lib/categoryHelper'
import { Recommendation } from '@/types/types'

const GridView = ({ recommendations }: { recommendations: Recommendation[] }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
    {recommendations.map((recommendation, index) => {
      const { id, gradient, icon } = getCategoryDetailsByRecommendationId(recommendation.id)

      return (
        <RecommendationCard
          key={recommendation.id}
          {...recommendation}
          categoryDetails={{
            id,
            gradient,
            icon,
          }}
          index={index}
          isAdminView
        />
      )
    })}
  </div>
)

export default GridView
