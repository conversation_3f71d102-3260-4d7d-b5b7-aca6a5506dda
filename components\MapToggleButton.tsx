'use client'

import IconButton from '@/components/IconButton'
import { Grid3X3, MapIcon } from 'lucide-react'
import useRecommendationListType from '@/hooks/useRecommendationListType'
import { cn } from '@/lib/utils'

type Props = {
  buttonClassName?: string
  iconClassName?: string
  gradient?: string
}

const MapToggleButton = ({ buttonClassName, iconClassName, gradient }: Props) => {
  const { recommendationListType, toggleRecommendationListType } = useRecommendationListType()
  const MapToggleIcon = recommendationListType === 'grid' ? MapIcon : Grid3X3

  return (
    <IconButton
      className={cn(
        recommendationListType === 'list' && 'bg-gradient-to-br !text-white',
        gradient,
        buttonClassName
      )}
      onClick={toggleRecommendationListType}
      Icon={MapToggleIcon}
      iconClassName={iconClassName}
      text={recommendationListType === 'grid' ? 'Map' : 'List'}
    />
  )
}

export default MapToggleButton
