import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

const FormSection = ({
  title,
  icon: Icon,
  className,
  children,
}: {
  title: string
  icon: LucideIcon
  children: React.ReactNode
  className?: string
}) => (
  <div className="flex flex-col gap-6">
    <h3 className="text-base flex items-center gap-2 font-semibold">
      <Icon size={20} />
      {title}
    </h3>
    <div className={cn('flex flex-col gap-4', className)}>{children}</div>
  </div>
)

export default FormSection
