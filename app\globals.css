@import url('https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css');
@import 'tailwindcss';
@import 'tw-animate-css';

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);
  --card-bg: color-mix(in oklab, var(--color-white) 85%, transparent);
  --card-hover: color-mix(in oklab, var(--color-white) 95%, transparent);
  --card-shadow: 0 10px 25px -5px color-mix(in oklab, var(--color-black) 8%, transparent), 0 20px 40px -12px rgba(0, 0, 0, 0.04), 0 0 0 1px color-mix(in oklab, var(--color-primary-text) 5%, transparent);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);
  --primary: hsl(221.2 83.2% 53.3%);
  --primary-text: var(--color-slate-900);
  --primary-foreground: hsl(210 40% 98%);
  --primary-gradient-from: var(--color-slate-50);
  --primary-gradient-via: var(--color-blue-50);
  --primary-gradient-to: var(--color-indigo-100);
  --secondary: hsl(210 40% 96%);
  --secondary-text: var(--color-slate-700);
  --secondary-foreground: hsl(222.2 84% 4.9%);
  --muted: hsl(210 40% 96%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  --accent: hsl(210 40% 96%);
  --accent-text: var(--color-slate-600);
  --accent-foreground: hsl(222.2 84% 4.9%);
  --success: var(--color-emerald-600);
  --success-text: var(--color-emerald-700);
  --success-bg: var(--color-emerald-200);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);

  --theme-switch-primary: var(--color-amber-500);
  --theme-switch-secondary: var(--color-blue-500);
  --theme-switch-accent: var(--color-amber-700);

  --logo-from: var(--color-indigo-600);
  --logo-via: var(--color-purple-600);
  --logo-to: var(--color-rose-600);
  
  --glass-gradient-from: color-mix(in oklab, var(--color-white) 30%, transparent);
  --glass-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);
  --glass-gradient-to: color-mix(in oklab, var(--color-slate-100) 20%, transparent);
  --glass-border: color-mix(in oklab, var(--color-white) 60%, transparent);

  --frosted-glass-gradient-from: color-mix(in oklab, var(--color-slate-200) 40%, transparent);
  --frosted-glass-gradient-via: color-mix(in oklab, var(--color-blue-100) 30%, transparent);
  --frosted-glass-gradient-to: color-mix(in oklab, var(--color-slate-200) 40%, transparent);

  --eat-icon-color: var(--color-rose-700);
  --eat-foreground-gradient-from: var(--color-rose-400);
  --eat-foreground-gradient-via: var(--color-orange-400);
  --eat-foreground-gradient-to: var(--color-amber-500);
  --eat-bg-gradient-from: color-mix(in oklab, var(--color-rose-50) 70%, transparent);
  --eat-bg-gradient-via: color-mix(in oklab, var(--color-orange-50) 50%, transparent);
  --eat-bg-gradient-to: color-mix(in oklab, var(--color-amber-50) 60%, transparent);
  --eat-accent: var(--color-rose-500);

  --drink-icon-color: var(--color-indigo-700);
  --drink-foreground-gradient-from: var(--color-indigo-400);
  --drink-foreground-gradient-via: var(--color-purple-500);
  --drink-foreground-gradient-to: var(--color-pink-500);
  --drink-bg-gradient-from: color-mix(in oklab, var(--color-indigo-50) 70%, transparent);
  --drink-bg-gradient-via: color-mix(in oklab, var(--color-indigo-50) 70%, transparent), color-mix(in oklab, var(--color-purple-50) 50%, transparent);
  --drink-bg-gradient-to: color-mix(in oklab, var(--color-pink-50) 60%, transparent);
  --drink-accent: var(--color-indigo-500);

  --do-icon-color: var(--color-teal-700);
  --do-foreground-gradient-from: var(--color-teal-400);
  --do-foreground-gradient-via: var(--color-emerald-500);
  --do-foreground-gradient-to: var(--color-green-500);
  --do-bg-gradient-from: color-mix(in oklab, var(--color-teal-50) 70%, transparent);
  --do-bg-gradient-via: color-mix(in oklab, var(--color-emerald-50) 50%, transparent);
  --do-bg-gradient-to: color-mix(in oklab, var(--color-green-50) 60%, transparent);
  --do-accent: var(--color-teal-500);

  --pulsating-orb-1-from: var(--color-slate-400);
  --pulsating-orb-1-to: var(--color-white);

  --pulsating-orb-2-from: var(--color-blue-200);
  --pulsating-orb-2-to: var(--color-indigo-300);

  --pulsating-orb-3-from: var(--color-rose-200);
  --pulsating-orb-3-to: var(--color-pink-300);

  --pulsating-orb-4-from: var(--color-emerald-200);
  --pulsating-orb-4-to: var(--color-teal-300);

  --tagline-star: var(--color-indigo-500);
}

.dark {
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);
  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);
  --card-bg: color-mix(in oklab, var(--color-white) 10%, transparent);
  --card-hover: color-mix(in oklab, var(--color-white) 20%, transparent);
  --card-shadow: 0 10px 25px -5px color-mix(in oklab, var(--color-black) 30%, transparent), 0 20px 40px -12px color-mix(in oklab, var(--color-black) 15%, transparent);
  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);
  --primary: hsl(217.2 91.2% 59.8%);
  --primary-text: var(--color-white);
  --primary-gradient-from: var(--color-slate-900);
  --primary-gradient-via: var(--color-purple-900);
  --primary-gradient-to: var(--color-indigo-900);
  --primary-foreground: hsl(222.2 84% 4.9%);
  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-text: var(--color-gray-200);
  --secondary-foreground: hsl(210 40% 98%);
  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);
  --accent: hsl(217.2 32.6% 17.5%);
  --accent-text: var(--color-gray-300);
  --accent-foreground: hsl(210 40% 98%);
  --success: var(--color-emerald-400);
  --success-text: var(--color-emerald-100);
  --success-bg: var(--color-emerald-500);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);

  --theme-switch-primary: var(--color-purple-500);
  --theme-switch-secondary: var(--color-indigo-500);
  --theme-switch-accent: var(--color-indigo-700);

  --logo-from: var(--color-purple-500);
  --logo-via: var(--color-pink-500);
  --logo-to: var(--color-orange-400);

  --glass-gradient-from: color-mix(in oklab, var(--color-white) 5%, transparent);
  --glass-gradient-via: transparent;
  --glass-gradient-to: color-mix(in oklab, var(--color-black) 10%, transparent);
  --glass-border: color-mix(in oklab, var(--color-white) 20%, transparent);

  --frosted-glass-gradient-from: color-mix(in oklab, var(--color-white) 20%, transparent);
  --frosted-glass-gradient-via: transparent;
  --frosted-glass-gradient-to: color-mix(in oklab, var(--color-white) 10%, transparent);

  --eat-icon-color: var(--color-amber-400);
  --eat-foreground-gradient-from: var(--color-amber-400);
  --eat-foreground-gradient-via: var(--color-orange-500);
  --eat-foreground-gradient-to: var(--color-red-500);
  --eat-bg-gradient-from: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
  --eat-bg-gradient-via: color-mix(in oklab, var(--color-orange-900) 10%, transparent);
  --eat-bg-gradient-to: color-mix(in oklab, var(--color-red-900) 20%, transparent);
  --eat-accent: var(--color-amber-500);

  --drink-icon-color: var(--color-purple-400);
  --drink-foreground-gradient-from: var(--color-purple-400);
  --drink-foreground-gradient-via: var(--color-purple-400);
  --drink-foreground-gradient-to: var(--color-pink-500);
  --drink-bg-gradient-from: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
  --drink-bg-gradient-via: color-mix(in oklab, var(--color-violet-900) 10%, transparent);
  --drink-bg-gradient-to: color-mix(in oklab, var(--color-pink-900) 20%, transparent);
  --drink-accent: var(--color-purple-500);

  --do-icon-color: var(--color-emerald-400);
  --do-foreground-gradient-from: var(--color-emerald-400);
  --do-foreground-gradient-via: var(--color-teal-500);
  --do-foreground-gradient-to: var(--color-cyan-500);
  --do-bg-gradient-from: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
  --do-bg-gradient-via: color-mix(in oklab, var(--color-teal-900) 10%, transparent);
  --do-bg-gradient-to: color-mix(in oklab, var(--color-cyan-900) 20%, transparent);
  --do-accent: var(--color-emerald-500);

  --pulsating-orb-1-from: var(--color-white);
  --pulsating-orb-1-to: var(--color-slate-400);

  --pulsating-orb-2-from: var(--color-cyan-400);
  --pulsating-orb-2-to: var(--color-blue-600);

  --pulsating-orb-3-from: var(--color-purple-400);
  --pulsating-orb-3-to: var(--color-pink-600);

  --pulsating-orb-4-from: var(--color-orange-400);
  --pulsating-orb-4-to: var(--color-red-600);

  --tagline-star: var(--color-yellow-400);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-card-bg: var(--card-bg);
  --color-card-hover: var(--card-hover);
  --shadow-card-shadow: var(--card-shadow);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-text: var(--primary-text);
  --color-primary-gradient-from: var(--primary-gradient-from);
  --color-primary-gradient-via: var(--primary-gradient-via);
  --color-primary-gradient-to: var(--primary-gradient-to);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-text: var(--secondary-text);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-text: var(--accent-text);
  --color-accent-foreground: var(--accent-foreground);
  --color-success: var(--success);
  --color-success-text: var(--success-text);
  --color-success-bg: var(--success-bg);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-theme-switch-primary: var(--theme-switch-primary);
  --color-theme-switch-secondary: var(--theme-switch-secondary);
  --color-theme-switch-accent: var(--theme-switch-accent);

  --color-logo-from: var(--logo-from);
  --color-logo-via: var(--logo-via);
  --color-logo-to: var(--logo-to);

  --color-glass-gradient-from: var(--glass-gradient-from);
  --color-glass-gradient-via: var(--glass-gradient-via);
  --color-glass-gradient-to: var(--glass-gradient-to);
  --color-glass-border: var(--glass-border);

  --color-frosted-glass-gradient-from: var(--frosted-glass-gradient-from);
  --color-frosted-glass-gradient-via: var(--frosted-glass-gradient-via);
  --color-frosted-glass-gradient-to: var(--frosted-glass-gradient-to);

  --color-eat-icon-color: var(--eat-icon-color);
  --color-eat-foreground-gradient-from: var(--eat-foreground-gradient-from);
  --color-eat-foreground-gradient-via: var(--eat-foreground-gradient-via);
  --color-eat-foreground-gradient-to: var(--eat-foreground-gradient-to);

  --color-eat-bg-gradient-from: var(--eat-bg-gradient-from);
  --color-eat-bg-gradient-via: var(--eat-bg-gradient-via);
  --color-eat-bg-gradient-to: var(--eat-bg-gradient-to);
  --color-eat-accent: var(--eat-accent);

  --color-drink-icon-color: var(--drink-icon-color);
  --color-drink-foreground-gradient-from: var(--drink-foreground-gradient-from);
  --color-drink-foreground-gradient-via: var(--drink-foreground-gradient-via);
  --color-drink-foreground-gradient-to: var(--drink-foreground-gradient-to);

  --color-drink-bg-gradient-from: var(--drink-bg-gradient-from);
  --color-drink-bg-gradient-via: var(--drink-bg-gradient-via);
  --color-drink-bg-gradient-to: var(--drink-bg-gradient-to);
  --color-drink-accent: var(--drink-accent);

  --color-do-icon-color: var(--do-icon-color);
  --color-do-foreground-gradient-from: var(--do-foreground-gradient-from);
  --color-do-foreground-gradient-via: var(--do-foreground-gradient-via);
  --color-do-foreground-gradient-to: var(--do-foreground-gradient-to);

  --color-do-bg-gradient-from: var(--do-bg-gradient-from);
  --color-do-bg-gradient-via: var(--do-bg-gradient-via);
  --color-do-bg-gradient-to: var(--do-bg-gradient-to);
  --color-do-accent: var(--do-accent);

  --color-tagline-star: var(--tagline-star);

  --color-pulsating-orb-1-from: var(--pulsating-orb-1-from);
  --color-pulsating-orb-1-to: var(--pulsating-orb-1-to);

  --color-pulsating-orb-2-from: var(--pulsating-orb-2-from);
  --color-pulsating-orb-2-to: var(--pulsating-orb-2-to);

  --color-pulsating-orb-3-from: var(--pulsating-orb-3-from);
  --color-pulsating-orb-3-to: var(--pulsating-orb-3-to);

  --color-pulsating-orb-4-from: var(--pulsating-orb-4-from);
  --color-pulsating-orb-4-to: var(--pulsating-orb-4-to);
  
  --animate-fade-up: fade-up var(--tw-animation-duration, 0.7s) forwards;
  --animate-fade-up-transform: fade-up-transform var(--tw-animation-duration, 0.7s) forwards;
}

@custom-variant dark (&:where(.dark, .dark *));
@custom-variant hoverActive (&:hover, &:active);
@custom-variant group-hoverActive (.group:hover &, .group:active &);

@layer base {
  * {
    @apply border-border
  }
  html, body {
    @apply bg-background text-primary-text relative m-0 p-0;
  }
  body {
    @apply text-xs;
  }

  #root {
    min-height: 100vh;
    min-height: 100dvh;
  }
  button,
  [type='button'],
  [role='button'],
  a {
    @apply cursor-pointer
  }
}

@utility transition-everything {
  transition-property: all, --tw-gradient-to, --tw-gradient-from, --tw-gradient-via;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

@utility primary-gradient {
  @apply from-primary-gradient-from via-primary-gradient-via to-primary-gradient-to;
}

@utility glass-gradient {
  @apply from-glass-gradient-from via-glass-gradient-via to-glass-gradient-to;
}

@utility frosted-glass-gradient {
  @apply from-frosted-glass-gradient-from via-frosted-glass-gradient-via to-frosted-glass-gradient-to;
}

@utility logo-gradient {
  @apply from-logo-from via-logo-via to-logo-to;
}

@utility eat-foreground-gradient {
  @apply from-eat-foreground-gradient-from via-eat-foreground-gradient-via to-eat-foreground-gradient-to;
}

@utility eat-bg-gradient {
  @apply from-eat-bg-gradient-from/20 via-eat-bg-gradient-via/20 to-eat-bg-gradient-to/20;
}

@utility drink-foreground-gradient {
  @apply from-drink-foreground-gradient-from via-drink-foreground-gradient-via to-drink-foreground-gradient-to;
}

@utility drink-bg-gradient {
  @apply from-drink-bg-gradient-from/20 via-drink-bg-gradient-via/20 to-drink-bg-gradient-to/20;
}

@utility do-foreground-gradient {
  @apply from-do-foreground-gradient-from via-do-foreground-gradient-via to-do-foreground-gradient-to;
}

@utility do-bg-gradient-gradient {
  @apply from-do-bg-gradient-from/20 via-do-bg-gradient-via/20 to-do-bg-gradient-to/20;
}

@utility pulsating-orb-1-gradient {
  @apply from-pulsating-orb-1-from to-pulsating-orb-1-to;
}

@utility pulsating-orb-2-gradient {
  @apply from-pulsating-orb-2-from to-pulsating-orb-2-to;
}

@utility pulsating-orb-3-gradient {
  @apply from-pulsating-orb-3-from to-pulsating-orb-3-to;
}

@utility pulsating-orb-4-gradient {
  @apply from-pulsating-orb-4-from to-pulsating-orb-4-to;
}


@keyframes fade-up {
  0% {
    opacity: 0;
    transform: translateY(2.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-up-transform {
  0% {
    opacity: 0;
    transform: translateY(2.5rem) scaleX(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scaleX(1);
  }
}
