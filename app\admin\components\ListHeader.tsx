import StatusCard from './StatusCard'
import { Database, Star, MapPin } from 'lucide-react'

type Props = {
  stats: {
    total: number
    byCategory: Record<string, number>
  }
  numberOfFilteredResults: number
}

const ListHeader = ({ stats: { total, byCategory }, numberOfFilteredResults }: Props) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    <StatusCard
      title="Total Places"
      value={total}
      icon={Database}
      iconClassname="text-blue-600 bg-blue-100"
    />
    <StatusCard
      title="Categories"
      value={Object.keys(byCategory).length}
      icon={Star}
      iconClassname="text-green-600 bg-green-100"
    />
    <StatusCard
      title="Filtered Results"
      value={numberOfFilteredResults}
      icon={MapPin}
      iconClassname="text-purple-600 bg-purple-100"
    />
  </div>
)

export default ListHeader
