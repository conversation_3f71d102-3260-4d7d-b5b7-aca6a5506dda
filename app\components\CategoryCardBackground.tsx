import { cn } from '@/lib/utils'

type Props = {
  gradient: string
}

const CategoryCardBackground = ({ gradient }: Props) => (
  <>
    <div
      className={cn(
        'absolute inset-0 bg-gradient-to-r transition-everything',
        gradient,
        'opacity-0 group-hoverActive:opacity-20 blur-xl'
      )}
    />
    <div className="absolute inset-0 rounded-xl p-px bg-gradient-to-r frosted-glass-gradient transition-colors" />
    <div className="absolute inset-0.5 rounded-xl bg-gradient-to-t from-transparent via-white/5 to-white/10 transition-colors" />
  </>
)

export default CategoryCardBackground
