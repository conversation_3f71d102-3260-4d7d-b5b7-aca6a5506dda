'use client'

import IconButton from '@/components/IconButton'
import { ArrowLeft } from 'lucide-react'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'

type Props = {
  buttonClassName?: string
  iconClassName?: string
}

const BackButton = ({ buttonClassName, iconClassName }: Props) => {
  const router = useRouter()
  const pathname = usePathname()

  const newPath = pathname.split('/').slice(0, -1).join('/') || '/'

  useEffect(() => {
    router.prefetch(newPath)
  }, [router, newPath])

  const handleClick = () => router.push(newPath)

  return (
    <IconButton
      className={buttonClassName}
      onClick={handleClick}
      Icon={ArrowLeft}
      iconClassName={iconClassName}
      text="Back"
    />
  )
}

export default BackButton
