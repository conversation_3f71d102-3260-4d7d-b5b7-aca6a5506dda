'use client'

import Image from 'next/image'
import { ComponentProps, useState } from 'react'
import { cn } from '@/lib/utils'

const ERROR_IMG_SRC =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgc3Ryb2tlPSIjMDAwIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBvcGFjaXR5PSIuMyIgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIzLjciPjxyZWN0IHg9IjE2IiB5PSIxNiIgd2lkdGg9IjU2IiBoZWlnaHQ9IjU2IiByeD0iNiIvPjxwYXRoIGQ9Im0xNiA1OCAxNi0xOCAzMiAzMiIvPjxjaXJjbGUgY3g9IjUzIiBjeT0iMzUiIHI9IjciLz48L3N2Zz4KCg=='

const ImageWithFallback = ({
  src,
  alt,
  style,
  className,
  ...props
}: ComponentProps<typeof Image>) => {
  const [didError, setDidError] = useState(false)

  const handleError = () => {
    setDidError(true)
  }

  return didError ? (
    <div
      className={cn('flex items-center justify-center bg-gray-100 align-middle', className)}
      style={style}
    >
      <Image
        src={ERROR_IMG_SRC}
        alt="Error loading image"
        width={448}
        height={336}
        {...props}
        className="w-full h-full object-cover"
        data-original-url={src}
      />
    </div>
  ) : (
    <Image
      src={src}
      alt={alt}
      className={className}
      width={448}
      height={336}
      style={style}
      {...props}
      onError={handleError}
    />
  )
}

export default ImageWithFallback
