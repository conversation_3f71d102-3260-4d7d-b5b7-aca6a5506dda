import { create } from 'zustand'
import { Recommendation } from '@/types/types'

interface RecommendationForm {
  recommendationId: Recommendation['id'] | 'new' | null
  setRecommendationId: (recommendationId: Recommendation['id'] | 'new') => void
  clearRecommendationId: () => void
}
export const useRecommendationForm = create<RecommendationForm>(set => ({
  recommendationId: null,
  setRecommendationId: recommendationId => set({ recommendationId }),
  clearRecommendationId: () => set({ recommendationId: null }),
}))
