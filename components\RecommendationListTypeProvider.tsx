'use client'

import { createContext, ReactNode, useState } from 'react'
import { RecommendationListType } from '@/types/types'

type ContextProps = {
  recommendationListType: RecommendationListType
  toggleRecommendationListType: () => void
}

export const RecommendationListTypeContext = createContext<ContextProps | null>(null)

const RecommendationListTypeProvider = ({ children }: Readonly<{ children: ReactNode }>) => {
  const [recommendationListType, setRecommendationListType] =
    useState<RecommendationListType>('grid')

  const toggleRecommendationListType = () => {
    setRecommendationListType(recommendationListType === 'grid' ? 'list' : 'grid')
  }

  const values = {
    recommendationListType,
    toggleRecommendationListType,
  }

  return <RecommendationListTypeContext value={values}>{children}</RecommendationListTypeContext>
}

export default RecommendationListTypeProvider
