import IconBadge from '@/components/IconBadge'
import { Minimize2, Maximize2, MapPin } from 'lucide-react'
import IconButton from './IconButton'

type Props = {
  isFullscreen: boolean
  toggleFullscreen: () => void
  isEditing?: boolean
  recommendationsLength?: number
}

const MapOverlay = ({
  isEditing,
  recommendationsLength,
  isFullscreen,
  toggleFullscreen,
}: Props) => (
  <>
    <IconButton
      Icon={isFullscreen ? Minimize2 : Maximize2}
      onClick={toggleFullscreen}
      variant="card"
      className="absolute top-4 left-4 z-20 p-2 bg-white border border-white/20 rounded-lg shadow-lg text-slate-700"
    />
    {(isEditing || recommendationsLength) && (
      <IconBadge
        Icon={MapPin}
        text={
          isEditing
            ? 'Click map or drag marker'
            : `${recommendationsLength} location${recommendationsLength !== 1 ? 's' : ''}`
        }
        variant="default"
        className="!bg-white text-slate-900 absolute bottom-4 right-4 z-20 py-1 shadow-md"
        size="sm"
      />
    )}
  </>
)

export default MapOverlay
