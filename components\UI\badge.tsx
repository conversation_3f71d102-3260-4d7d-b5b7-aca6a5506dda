import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center justify-center rounded-full border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive !transition-everything overflow-hidden',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground [a&]:hoverActive:bg-primary/90',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground [a&]:hoverActive:bg-secondary/90',
        destructive:
          'border-transparent bg-destructive text-white [a&]:hoverActive:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'text-foreground [a&]:hoverActive:bg-accent [a&]:hoverActive:text-accent-foreground',
        glass:
          'bg-white/10 hoverActive:bg-white/20 border-[1px] border-transparent text-primary-text shadow-sm hoverActive:shadow-md',
        frostedGlass:
          'bg-white/15 hoverActive:bg-white/30 backdrop-blur-md border-[1px] border-transparent text-primary-text shadow-sm hoverActive:shadow-md',
        card: 'bg-card-bg hoverActive:bg-card-hover border-[1px] border-transparent text-primary-text shadow-sm hoverActive:shadow-md',
      },
      size: {
        sm: 'text-xs gap-1 px-2 py-0.5',
        default: 'text-xs gap-1 px-3 py-1.5',
        lg: 'text-sm gap-2 px-6 py-3',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const Badge = ({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) => {
  const Comp = asChild ? Slot : 'span'

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant, size }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }
