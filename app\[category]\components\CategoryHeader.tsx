'use client'

import { cn } from '@/lib/utils'
import { MapPin, Sparkles } from 'lucide-react'
import IconBadge from '@/components/IconBadge'
import MapToggleButton from '@/components/MapToggleButton'
import Header from '@/components/Header'
import { Category } from '@/types/types'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import HeaderTitle from '@/components/HeaderTitle'
import useScrolled from '@/hooks/useScrolled'
import getHeaderScrollConfig from '@/lib/getHeaderScrollConfig'

type CategoryHeaderProps = {
  category: Category
  recommendationsLength: number
}

const CategoryHeader = ({ category, recommendationsLength }: CategoryHeaderProps) => {
  const { title, accentIcon, emoji, iconColor, gradient } = getCategoryDetailsById(category)
  const { scrolled } = useScrolled()
  const headerScrollConfig = getHeaderScrollConfig(scrolled)
  const { buttonScale, buttonPadding, buttonFrontSize, iconSize } = headerScrollConfig

  const StatBadges = (
    <>
      <IconBadge
        Icon={MapPin}
        text={recommendationsLength.toString()}
        variant="frostedGlass"
        className="border-none"
        size="sm"
      />
      <IconBadge
        Icon={Sparkles}
        text="Curated"
        variant="frostedGlass"
        className="border-none"
        iconClassName="text-yellow-400"
        size="sm"
      />
    </>
  )

  return (
    <Header>
      <HeaderTitle
        title={title}
        Icon={accentIcon}
        emoji={emoji}
        scrolled={scrolled}
        StatBadges={StatBadges}
        iconColor={iconColor}
        {...headerScrollConfig}
      />
      <MapToggleButton
        gradient={gradient}
        buttonClassName={cn(buttonScale, buttonPadding, buttonFrontSize)}
        iconClassName={iconSize}
      />
    </Header>
  )
}

export default CategoryHeader
