import Header from './components/Header'
import List from './components/List'
import Footer from './components/Footer'
import ThemeHolder from './components/ThemeHolder'
import RecommendationForm from './components/RecommendationFormDialog'
import { mockRecommendations } from '@/data/mockData'

const page = () => {
  // TODO: fetch recommendations from DB
  const recommendations = [
    ...mockRecommendations.eat,
    ...mockRecommendations.drink,
    ...mockRecommendations.do,
  ]

  return (
    <div className="relative min-h-screen bg-gray-50 text-gray-900 flex flex-col overflow-x-hidden">
      <RecommendationForm />
      <ThemeHolder />
      <Header />
      <List recommendations={recommendations} />
      <Footer />
    </div>
  )
}

export default page
