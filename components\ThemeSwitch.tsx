'use client'

import { useTheme } from 'next-themes'
import { cn } from '@/lib/utils'
import { Moon, Sun } from 'lucide-react'
import { CSSProperties } from 'react'

type ThemeSwitchProps = {
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

const sizeClasses = {
  sm: 'w-12 h-7',
  md: 'w-16 h-9',
  lg: 'w-20 h-11',
}

const knobSizes = {
  sm: 'size-5',
  md: 'size-7',
  lg: 'size-9',
}

const iconSizes = {
  sm: 12,
  md: 16,
  lg: 20,
}

const ThemeSwitch = ({ className = '', size = 'md' }: ThemeSwitchProps) => {
  const { theme, setTheme } = useTheme()
  const turnedOn = theme === 'light'

  const toggleTheme = () => {
    setTheme(turnedOn ? 'dark' : 'light')
  }

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        sizeClasses[size],
        'relative inline-flex items-center rounded-full transition-everything ease-in-out group backdrop-blur-[10px] bg-theme-switch-secondary/20 border-[1px] border-theme-switch-secondary/30 duration-300 **:duration-300 shadow-[0_4px_15px_color-mix(in_oklab,var(--theme-switch-secondary)20%,transparent),inset_0_1px_0_rgba(255,255,255,0.15)]',
        className
      )}
      aria-label={`Switch to ${turnedOn ? 'dark' : 'light'} theme`}
      suppressHydrationWarning
    >
      <div className="absolute inset-0 rounded-full opacity-0 group-hoverActive:opacity-100 blur-xs transition-everything bg-radial from-theme-switch-primary/30 to-70%" />
      <div className="absolute inset-1 rounded-full transition-colors bg-gradient-to-r from-theme-switch-primary/30 to-theme-switch-secondary/30" />
      <div className="absolute inset-0 flex items-center justify-between px-2 z-10 text-accent-text/30 transition-colors">
        <Moon size={iconSizes[size]} />
        <Sun size={iconSizes[size]} />
      </div>
      <div
        className={cn(
          knobSizes[size],
          'absolute top-1/2 transform -translate-y-1/2 rounded-full transition-everything ease-in-out flex items-center justify-center group-hoverActive:scale-110 z-20 bg-gradient-to-br from-[color-mix(in_oklab,var(--theme-switch-primary)60%,white)] via-[color-mix(in_oklab,var(--theme-switch-primary)60%,white)] to-theme-switch-primary shadow-[0_2px_8px_color-mix(in_oklab,var(--theme-switch-accent)40%,transparent),inset_0_1px_0_rgba(255,255,255,0.6)]'
        )}
        style={
          {
            '--knob-size': size === 'sm' ? '1.2rem' : size === 'md' ? '1.75rem' : '2.25rem',
            left: turnedOn ? 'calc(100% - 0.25rem - var(--knob-size))' : '0.25rem',
          } as CSSProperties
        }
      >
        {turnedOn ? (
          <Sun
            size={iconSizes[size]}
            className="text-theme-switch-accent group-hoverActive:rotate-90 transition-all"
          />
        ) : (
          <Moon
            size={iconSizes[size]}
            className="text-theme-switch-accent group-hoverActive:-rotate-12 transition-all"
          />
        )}
      </div>
    </button>
  )
}

export default ThemeSwitch
