'use client'

import { Share } from 'lucide-react'
import { MouseEvent, useState } from 'react'
import { cn } from '@/lib/utils'
import IconButton from '@/components/IconButton'
import { toast } from 'sonner'

type Props = {
  title: string | undefined
  text: string | undefined
  url: string | undefined
  className?: string
  iconClassName?: string
}

const ShareButton = ({ className, iconClassName, title, text, url }: Props) => {
  const [loading, setLoading] = useState(false)

  const handleShare = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    const shareData = {
      title,
      text,
      url,
    }

    try {
      setLoading(true)
      if (
        (!title && !text && !url) ||
        (navigator.share && navigator.canShare && navigator.canShare(shareData))
      ) {
        navigator
          .share(shareData)
          .then(() => {
            toast.success('Shared!')
          })
          .catch(() => {
            toast.error('Unable to share at this time')
          })
      } else {
        if (url && navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(url)
          toast.success('Link copied to clipboard!')
        } else {
          toast.error('Unable to share at this time')
        }
      }
    } catch (error: unknown) {
      console.error('Error sharing:', error)
      toast.error('Unable to share at this time')
    } finally {
      setLoading(false)
    }
  }

  return (
    <IconButton
      Icon={Share}
      variant="card"
      className={cn('p-2.5 group/share', className)}
      iconClassName={cn('group-hover/share:text-cyan-400 duration-300', iconClassName)}
      onClick={handleShare}
      disabled={loading}
    />
  )
}

export default ShareButton
